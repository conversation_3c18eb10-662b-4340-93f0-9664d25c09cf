package com.ruoyi.callback.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.callback.domain.CallbackVo;
import com.ruoyi.callback.service.*;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.PayConstants;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.common.redis.util.RedisUtil;
import com.ruoyi.system.api.constants.TaskConstants;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.mapper.FrontOrdersMapper;
import com.ruoyi.system.api.mapper.FrontUserMapper;
import com.ruoyi.system.api.service.UserRechargeService;
import com.ruoyi.system.api.utils.CommonUtil;
import com.ruoyi.system.api.utils.WxPayUtil;
import com.ruoyi.system.api.vo.AttachVo;
import lombok.RequiredArgsConstructor;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Author: suhai
 * @Date: 2025/6/18
 * @Description:
 */

/**
 * 订单支付回调 CallbackService 实现类
 */

@Service
@RequiredArgsConstructor
public class CallbackServiceImpl implements CallbackService {

    private static final Logger logger = LoggerFactory.getLogger(CallbackServiceImpl.class);


    private final FrontUserMapper frontUserMapper;

    private final UserRechargeService userRechargeService;

    private final RechargePayService rechargePayService;

    private final FrontOrdersService frontOrdersService;

    private final WechatPayService wechatPayInfoService;

    private final RedisUtil redisUtil;

    private final TransactionTemplate transactionTemplate;

    private final FrontOrdersMapper frontOrdersMapper;

    private final GiftPayService giftPayService;



    /**
     * 微信支付回调
     *
     * @param xmlInfo
     * @return
     */
    @Override
    public String weChat(String xmlInfo) {
        StringBuffer sb = new StringBuffer();
        sb.append("<xml>");
        try {
            HashMap<String, Object> map = WxPayUtil.processResponseXml(xmlInfo);
            // 通信是否成功
            String returnCode = (String) map.get("return_code");
            if (!returnCode.equals(PayConstants.SUCCESS)) {
                sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                sb.append("</xml>");
                logger.error("wechat callback error : wx pay return code is fail returnMsg : " + map.get("return_msg"));
                return sb.toString();
            }
            // 交易是否成功
            String resultCode = (String) map.get("result_code");
            if (!resultCode.equals(PayConstants.SUCCESS)) {
                sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                sb.append("</xml>");
                logger.error("wechat callback error : wx pay result code is fail");
                return sb.toString();
            }
            //解析xml
            CallbackVo callbackVo = CommonUtil.mapToObj(map , CallbackVo.class);
            AttachVo attachVo = JSONObject.toJavaObject(JSONObject.parseObject(callbackVo.getAttach()) , AttachVo.class);

            //判断openid
            FrontUser frontUser = frontUserMapper.selectFrontUserById(Long.valueOf(attachVo.getUserId()));
            if (ObjectUtil.isNull(frontUser)) {
                //用户信息错误
                throw new GlobalException("用户信息错误！");
            }
            //根据类型判断是订单或者充值
            if (!PayConstants.SERVICE_PAY_TYPE_ORDER.equals(attachVo.getType()) && !PayConstants.SERVICE_PAY_TYPE_RECHARGE.equals(attachVo.getType())) {
                logger.error("wechat pay err : 未知的支付类型==》" + callbackVo.getOutTradeNo());
                throw new GlobalException("未知的支付类型！");
            }
            // 订单
            if (PayConstants.SERVICE_PAY_TYPE_ORDER.equals(attachVo.getType())) {
                FrontOrders orderInfo =  getOrderInfo(attachVo.getUserId() , callbackVo.getOutTradeNo());
                if (orderInfo == null) {
                    logger.error("wechat pay error : 订单信息不存在==》" + callbackVo.getOutTradeNo());
                    throw new GlobalException("wechat pay error : 订单信息不存在==》" + callbackVo.getOutTradeNo());
                }
                if (orderInfo.getPaid()) {
                    logger.error("wechat pay error : 订单已处理==》" + callbackVo.getOutTradeNo());
                    return successResponse();
                }
                WechatPayInfo wechatPayInfo = wechatPayInfoService.getByNo(orderInfo.getOutTradeNo());
                if (ObjectUtil.isNull(wechatPayInfo)) {
                    logger.error("wechat pay error : 微信订单信息不存在==》" + callbackVo.getOutTradeNo());
                    throw new GlobalException("wechat pay error : 微信订单信息不存在==》" + callbackVo.getOutTradeNo());
                }
                updateWechatPayInfo(wechatPayInfo, callbackVo);
                // 更新订单状态并处理组合支付中的其他支付方式
                Boolean execute = transactionTemplate.execute(e -> {
                    FrontOrders frontOrders = new FrontOrders();
                    frontOrders.setId(orderInfo.getId());
                    frontOrders.setPaid(true);
                    frontOrders.setPayTime(LocalDateTime.now());
                    frontOrders.setStatus(1);
                    frontOrdersService.updateById(frontOrders);
                    wechatPayInfoService.updateById(wechatPayInfo);

                    // 处理微信支付成功后的其他支付方式（积分、余额、优惠券等）
                    processOtherPaymentMethodsAfterWechat(orderInfo);

                    return Boolean.TRUE;
                });
                if (Boolean.FALSE.equals(execute)) {
                    logger.error("wechat pay error : 订单更新失败==》" + callbackVo.getOutTradeNo());
                    return successResponse();
                }
                redisUtil.lPush(TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER, orderInfo);
            }

            // 充值
            if (PayConstants.SERVICE_PAY_TYPE_RECHARGE.equals(attachVo.getType())) {
                UserRecharge userRecharge = new UserRecharge();
                userRecharge.setOrderId(callbackVo.getOutTradeNo());
                userRecharge.setUid(attachVo.getUserId());
                userRecharge = userRechargeService.getInfoByEntity(userRecharge);
                if (ObjectUtil.isNull(userRecharge)) {
                    throw new GlobalException("没有找到订单信息");
                }
                if (userRecharge.getPaid()) {
                    sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                    sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                    sb.append("</xml>");
                    return sb.toString();
                }
                // 支付成功处理
                Boolean rechargePayAfter = rechargePayService.paySuccess(userRecharge);
                if (!rechargePayAfter) {
                    logger.error("wechat pay error : 数据保存失败==》" + callbackVo.getOutTradeNo());
                    throw new GlobalException("wechat pay error : 数据保存失败==》" + callbackVo.getOutTradeNo());
                }
            }

            // 礼品卡
            if (PayConstants.SERVICE_PAY_TYPE_GIFT.equals(attachVo.getType())) {
                UserRecharge userRecharge = new UserRecharge();
                userRecharge.setOrderId(callbackVo.getOutTradeNo());
                userRecharge.setUid(attachVo.getUserId());
                userRecharge = userRechargeService.getInfoByEntity(userRecharge);
                if (ObjectUtil.isNull(userRecharge)) {
                    throw new GlobalException("没有找到订单信息");
                }
                if (userRecharge.getPaid()) {
                    sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
                    sb.append("<return_msg><![CDATA[OK]]></return_msg>");
                    sb.append("</xml>");
                    return sb.toString();
                }
                // 支付成功处理
                Boolean giftPayAfter = giftPayService.paySuccess(userRecharge);
                if (!giftPayAfter) {
                    logger.error("wechat pay error : 数据保存失败==》" + callbackVo.getOutTradeNo());
                    throw new GlobalException("wechat pay error : 数据保存失败==》" + callbackVo.getOutTradeNo());
                }
            }
            sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
            sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        } catch (Exception e) {
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[").append(e.getMessage()).append("]]></return_msg>");
            logger.error("wechat pay error : 业务异常==》" + e.getMessage());
        }
        sb.append("</xml>");
        return sb.toString();
    }

    private void updateWechatPayInfo(WechatPayInfo wechatPayInfo, CallbackVo callbackVo) {
        wechatPayInfo.setIsSubscribe(callbackVo.getIsSubscribe())
                .setBankType(callbackVo.getBankType())
                .setCashFee(callbackVo.getCashFee())
                .setCouponFee(callbackVo.getCouponFee())
                .setTransactionId(callbackVo.getTransactionId())
                .setTimeEnd(callbackVo.getTimeEnd());
    }

    private FrontOrders getOrderInfo(Integer userId , String outTradeNo) {
        FrontOrders frontOrders = new FrontOrders();
        frontOrders.setOutTradeNo(outTradeNo);
        frontOrders.setUserId(Long.valueOf(userId));
        return frontOrdersService.getInfoByEntity(frontOrders);
    }

    private String successResponse() {
        StringBuffer sb = new StringBuffer();
        sb.append("<return_code><![CDATA[SUCCESS]]></return_code>")
                .append("<return_msg><![CDATA[OK]]></return_msg>")
                .append("</xml>");
        return sb.toString();
    }

    private static final List<String> list = new ArrayList<>();
    static {
        list.add("total_fee");
        list.add("cash_fee");
        list.add("coupon_fee");
        list.add("coupon_count");
        list.add("refund_fee");
        list.add("settlement_refund_fee");
        list.add("settlement_total_fee");
        list.add("cash_refund_fee");
        list.add("coupon_refund_fee");
        list.add("coupon_refund_count");
    }

    @Override
    public String weChatRefund(String xmlInfo) {
        MyRecord notifyRecord = new MyRecord();
        MyRecord refundRecord = refundNotify(xmlInfo, notifyRecord);
        if ("fail".equals(refundRecord.getStr("status"))) {
            logger.error("微信退款回调失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }

        if (!refundRecord.getBoolean("isRefund")) {
            logger.error("微信退款回调失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }
        String outRefundNo = notifyRecord.getStr("out_refund_no");
        FrontOrders refundOrder = getInfoException(outRefundNo);
        if (ObjectUtil.isNull(refundOrder)) {
            logger.error("微信退款订单查询失败==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }
        if (refundOrder.getStatus() == 7) {
            logger.warn("微信退款订单已确认成功==>" + refundRecord.getColumns() + ", rawData==>" + xmlInfo + ", data==>" + notifyRecord);
            return refundRecord.getStr("returnXml");
        }
        // 退款task
        redisUtil.lPush(PayConstants.ORDER_TASK_REDIS_KEY_AFTER_REFUND_BY_USER, refundOrder);
        return refundRecord.getStr("returnXml");
    }

    private FrontOrders getInfoException(String orderNo) {
        LambdaQueryWrapper<FrontOrders> lqw = Wrappers.lambdaQuery();
        lqw.eq(FrontOrders::getOrderNumber, orderNo);
        FrontOrders frontOrders = frontOrdersMapper.selectOne(lqw);
        if (ObjectUtil.isNull(frontOrders)) {
            throw new GlobalException("没有找到订单信息");
        }
        return frontOrders;
    }

    /**
     * 支付订单回调通知
     * @return MyRecord
     */
    private MyRecord refundNotify(String xmlInfo, MyRecord notifyRecord) {
        MyRecord refundRecord = new MyRecord();
        refundRecord.set("status", "fail");
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        if(StrUtil.isBlank(xmlInfo)){
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[xmlInfo is blank]]></return_msg>");
            sb.append("</xml>");
            logger.error("wechat refund callback error : " + sb.toString());
            return refundRecord.set("returnXml", sb.toString()).set("errMsg", "xmlInfo is blank");
        }

        Map<String, String> respMap;
        try {
            respMap = WxPayUtil.xmlToMap(xmlInfo);
        } catch (Exception e) {
            sb.append("<return_code><![CDATA[FAIL]]></return_code>");
            sb.append("<return_msg><![CDATA[").append(e.getMessage()).append("]]></return_msg>");
            sb.append("</xml>");
            logger.error("wechat refund callback error : " + e.getMessage());
            return refundRecord.set("returnXml", sb.toString()).set("errMsg", e.getMessage());
        }

        notifyRecord.setColums(_strMap2ObjMap(respMap));
        String return_code = respMap.get("return_code");
        if (return_code.equals(Constants.SUCCESS)) {
            String signKey = "frB6YEFq2FxlJrJJ6K0hATIiSawPexfg";
            // 解码加密信息
            String reqInfo = respMap.get("req_info");
            System.out.println("encodeReqInfo==>" + reqInfo);
            try {
                String decodeInfo = decryptToStr(reqInfo, signKey);
                Map<String, String> infoMap = WxPayUtil.xmlToMap(decodeInfo);
                notifyRecord.setColums(_strMap2ObjMap(infoMap));

                String refund_status = infoMap.get("refund_status");
                refundRecord.set("isRefund", refund_status.equals(Constants.SUCCESS));
            } catch (Exception e) {
                refundRecord.set("isRefund", false);
                logger.error("微信退款回调异常，e==》" + e.getMessage());
            }
        } else {
            notifyRecord.set("return_msg", respMap.get("return_msg"));
            refundRecord.set("isRefund", false);
        }
        sb.append("<return_code><![CDATA[SUCCESS]]></return_code>");
        sb.append("<return_msg><![CDATA[OK]]></return_msg>");
        sb.append("</xml>");
        return refundRecord.set("returnXml", sb.toString()).set("status", "ok");
    }



    /**
     * java自带的是PKCS5Padding填充，不支持PKCS7Padding填充。
     * 通过BouncyCastle组件来让java里面支持PKCS7Padding填充
     * 在加解密之前加上：Security.addProvider(new BouncyCastleProvider())，
     * 并给Cipher.getInstance方法传入参数来指定Java使用这个库里的加/解密算法。
     */
    public static String decryptToStr(String reqInfo, String signKey) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
//        byte[] decodeReqInfo = Base64.decode(reqInfo);
        byte[] decodeReqInfo = base64DecodeJustForWxPay(reqInfo).getBytes(StandardCharsets.ISO_8859_1);
        SecretKeySpec key = new SecretKeySpec(SecureUtil.md5(signKey).toLowerCase().getBytes(), "AES");
        Cipher cipher;
        cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
        cipher.init(Cipher.DECRYPT_MODE, key);
        return new String(cipher.doFinal(decodeReqInfo), StandardCharsets.UTF_8);
    }

    /**
     * 仅仅为微信解析密文使用
     * @param source 待解析密文
     * @return 结果
     */
    public static String base64DecodeJustForWxPay(final String source) {
        String result = "";
        final Base64.Decoder decoder = Base64.getDecoder();
        try {
            result = new String(decoder.decode(source), "ISO-8859-1");
        } catch (final UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return result;
    }

    private Map<String, Object> _strMap2ObjMap(Map<String, String> params) {
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (list.contains(entry.getKey())) {
                try {
                    map.put(entry.getKey(), Integer.parseInt(entry.getValue()));
                } catch (NumberFormatException e) {
                    map.put(entry.getKey(), 0);
                    logger.error("字段格式错误，key==》" + entry.getKey() + ", value==》" + entry.getValue());
                }
                continue;
            }

            map.put(entry.getKey(), entry.getValue());
        }
        return map;
    }
}
