package com.ruoyi.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.check.domain.entity.FrontReportResultEntity;
import com.ruoyi.check.mapper.FrontNumberMapper;
import com.ruoyi.check.mapper.FrontReportResultMapper;
import com.ruoyi.system.api.domain.dto.UserCountDTO;
import com.ruoyi.system.api.domain.vo.UserCouponVO;
import com.ruoyi.system.api.mapper.FrontCouponInfoMapper;
import com.ruoyi.system.api.mapper.FrontGiftInfoMapper;
import com.ruoyi.system.api.oss.OssUrlCleanerUtil;
import com.ruoyi.system.api.vo.UserGiftVO;
import com.ruoyi.user.domain.dto.*;
import com.ruoyi.user.domain.entity.*;
import com.ruoyi.user.domain.vo.*;
import com.ruoyi.user.mapper.*;
import com.ruoyi.user.service.FrontPeriodInfoService;
import com.ruoyi.user.service.FrontUserManagerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:44
 */
@Slf4j
@Service
public class FrontUserManagerServiceImpl implements FrontUserManagerService {

    @Autowired
    @Qualifier("userManagerMapper")
    private FrontUserMapper frontUserMapper;
    @Resource(name = "ruoyi-thread-pool-uniuser-query")
    private ExecutorService executorService;
    @Resource
    private FrontGiftInfoMapper  frontGiftInfoMapper;
    @Resource
    private FrontCouponInfoMapper frontCouponInfoMapper;
    @Resource
    private FrontFamilyMapper frontFamilyMapper;
    @Resource
    private FrontPeriodInfoService frontPeriodInfoService;
    @Resource
    private FrontAddressMapper frontAddressMapper;
    @Autowired
    @Qualifier("systemFrontOrdersMapper")
    private FrontOrdersMapper frontOrdersMapper;
    @Autowired
    @Qualifier("systemFrontOrdersGoodsMapper")
    private FrontOrdersGoodsMapper  frontOrdersGoodsMapper;
    @Resource
    private FrontReportResultMapper frontReportResultDao;
    @Resource
    private FrontNumberMapper frontNumberMapper;
    @Autowired
    @Qualifier("userFrontGoodsMapper")
    private FrontGoodsMapper frontGoodsMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;



    @Override
    public List<UserListVO> getUserList(UserManagerQueryDTO queryDTO) {
        List<UserListVO> list = frontUserMapper.getUserList(queryDTO);
        if (CollectionUtils.isEmpty(list)){
            return list;
        }
        List<Long> uidList = list.stream().map(UserListVO::getUid).collect(Collectors.toList());
        //查询余额
        Future<List<BalanceTotalDTO>> balanceFuture = executorService.submit(() -> frontUserMapper.getBalanceTotal(uidList));
        //查询优惠券
        Future<List<UserCountDTO>> couponFuture = executorService.submit(() -> frontCouponInfoMapper.getUserCouponCountByUids(uidList));
        //查询礼品卡
        Future<List<UserCountDTO>> giftFuture = executorService.submit(()->frontGiftInfoMapper.getUserGiftCountByUids(uidList));
        try {
            Map<Long, String> balanceTotalMap = balanceFuture.get().stream().collect(Collectors.toMap(BalanceTotalDTO::getUid, BalanceTotalDTO::getBalanceTotal));
            Map<Long, Integer> couponMap = couponFuture.get().stream().collect(Collectors.toMap(UserCountDTO::getUid, UserCountDTO::getCount));
            Map<Long, Integer> giftMap = giftFuture.get().stream().collect(Collectors.toMap(UserCountDTO::getUid, UserCountDTO::getCount));
            list.forEach(item -> {
                item.setBalanceTotal(new BigDecimal(balanceTotalMap.getOrDefault(item.getUid(), "0.00")));
                item.setCouponCount(couponMap.getOrDefault(item.getUid(), 0));
                item.setGiftCount(giftMap.getOrDefault(item.getUid(), 0));
            });
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取用户信息异常",e);
        }
        return list;
    }

    @Override
    public void freezeUser(FreezeDTO dto) {
        frontUserMapper.update(null,new LambdaUpdateWrapper<FrontUserEntity>()
                .set(FrontUserEntity::getStatus,dto.getUstatus())
                .eq(FrontUserEntity::getId,dto.getUid()));
    }

    @Override
    public void unfreezeUser(FreezeDTO dto) {
        frontUserMapper.update(null,new LambdaUpdateWrapper<FrontUserEntity>()
                .set(FrontUserEntity::getStatus,dto.getUstatus())
                .eq(FrontUserEntity::getId,dto.getUid()));
    }

    @Override
    public List<UserBalanceVO> getUserBalanceList(Long uid) {
        //TODO  查询用户余额
        return Collections.emptyList();
    }

    @Override
    public List<UserCouponVO> getUserCouponList(Long uid) {
        return frontCouponInfoMapper.getUserCouponList(uid);
    }

    @Override
    public List<UserGiftVO> getUserGiftList(Long uid) {
        return frontGiftInfoMapper.getUserGiftList(uid);
    }

    @Override
    public List<UserSourceVO> getUserSourceList(Long uid) {
        //TODO  查询用户积分
        return Collections.emptyList();
    }

    @Override
    public UniUserDetailsVO getUniUserDetails(Long uid) {
        UniUserDetailsVO vo = new UniUserDetailsVO();

        //查询基本信息
        Future<FrontUserEntity> uniuserFuture = executorService.submit(() ->
                frontUserMapper.selectOne(new LambdaQueryWrapper<FrontUserEntity>()
                        .eq(FrontUserEntity::getId, uid)));

        //查询家庭成员
        Future<List<FrontFamilyEntity>> familyListFuture = executorService.submit(() ->
                frontFamilyMapper.selectList(new LambdaQueryWrapper<FrontFamilyEntity>()
                        .eq(FrontFamilyEntity::getUserId, uid)));
        //查询统计信息
        Future<UserStatisticsVO> balanceTotalListFuture = executorService.submit(() ->
                frontOrdersMapper.getOrderStatistics(uid));
        //查询地址信息
        Future<List<FrontAddressEntity>> addressListFuture = executorService.submit(() ->
                frontAddressMapper.selectList(new LambdaQueryWrapper<FrontAddressEntity>()
                        .eq(FrontAddressEntity::getUserId, uid)));
        //查询周期信息
        Future<FrontPeriodInfoEntity> periodListFuture = executorService.submit(() ->
                frontPeriodInfoService.selectFrontPeriodInfoByMonth(DateFormatUtils.format(new Date(),"yyyy-MM"),uid));
        try {
            FrontUserEntity frontUserEntity = uniuserFuture.get();
            frontUserEntity.setUserIcon(ossUrlCleanerUtil.getSignatureUrl(frontUserEntity.getUserIcon()));
            vo.setUserBaseInfo(uniuserFuture.get());
            vo.setFamilyList(familyListFuture.get());
            vo.setAddressList(addressListFuture.get());
            vo.setStatisticsInfo(balanceTotalListFuture.get());
            vo.setPeriodInfo(periodListFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            log.warn("获取用户信息异常",e);
        }
        return vo;
    }

    @Override
    public List<UserOrderInfoVO> getOrderList(UserOrderQueryDTO queryDTO) {
        return frontOrdersMapper.getOrderList(queryDTO);
    }

    @Override
    public List<UserReportInfoVO> getCheckListPage(UserReportQueryDTO queryDTO) {
        List<UserReportInfoVO> list = frontNumberMapper.getCheckList(queryDTO);
        if(!CollectionUtils.isEmpty(list)){
            List<String> itemCodes = list.stream().map(UserReportInfoVO::getItemCode).collect(Collectors.toList());
            List<FrontReportResultEntity> checkResultList = frontReportResultDao.selectList(new LambdaQueryWrapper<FrontReportResultEntity>()
                    .in(FrontReportResultEntity::getItemCode, itemCodes));
            for (UserReportInfoVO userReportInfoVO : list) {
                List<FrontReportResultEntity> resultList = checkResultList.stream().filter(f -> f.getItemCode().equals(userReportInfoVO.getItemCode())).collect(Collectors.toList());
                userReportInfoVO.setCheckResultList(resultList);
            }
        }
        return list;
    }

    @Override
    public List<FrontGoodsEntity> getUserSpcoList(Long uid) {
        return frontGoodsMapper.getUserSpcoList(uid);
    }
}
