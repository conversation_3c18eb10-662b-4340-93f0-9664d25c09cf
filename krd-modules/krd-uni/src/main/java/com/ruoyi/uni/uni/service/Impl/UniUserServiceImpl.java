package com.ruoyi.uni.uni.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jthinking.common.util.ip.IPInfo;
import com.jthinking.common.util.ip.IPInfoUtils;
import com.ruoyi.common.core.exception.GlobalException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.domain.resp.FrontCoupon;
import com.ruoyi.system.api.domain.user.FrontSource;
import com.ruoyi.system.api.mapper.*;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.api.domain.vo.UniUserVo;
import com.ruoyi.system.api.utils.CommonUtil;
import com.ruoyi.uni.uni.mapper.UniFrontGoodsMapper;
import com.ruoyi.uni.uni.utils.oss.OssUrlCleanerUtil;
import com.ruoyi.uni.uni.service.IUniUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName UserUniServiceImpl
 * @Description 用户相关操作 业务实现类
 * <AUTHOR>
 * @Date 2025/5/19 上午10:22
 */
@Slf4j
@Service
public class UniUserServiceImpl implements IUniUserService {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private OssUrlCleanerUtil ossUrlCleanerUtil;

    @Autowired
    private FrontGiftInfoMapper frontGiftInfoMapper;

    @Autowired
    private FrontCouponInfoMapper frontCouponInfoMapper;

    @Autowired
    private FrontBalanceInfoMapper frontBalanceInfoMapper;

    @Autowired
    private FrontOrdersMapper frontOrdersMapper;

    @Autowired
    private FrontOrdersGoodsMapper frontOrdersGoodsMapper;

    @Autowired
    private UniFrontGoodsMapper uniFrontGoodsMapper;

    @Autowired
    private FrontSourceMapper frontSourceMapper;

    @Autowired
    private FrontGiftUserInfoMapper frontGiftUserInfoMapper;

    @Autowired
    private FrontGiftMapper frontGiftMapper;

    @Autowired
    private FrontCouponMapper frontCouponMapper;

    @Override
    public Long getUserId() {
        return tokenService.getLoginUser().getFrontUser().getId();
    }

    @Override
    public FrontUser getUserInfo() {
        // 获取用户id
        Long userId = tokenService.getLoginUser().getFrontUser().getId();
        FrontUser frontUser = frontUserMapper.selectFrontUserById(userId);
        if (frontUser.getUserIcon() != null){
            frontUser.setUserIcon(ossUrlCleanerUtil.getSignatureUrl(frontUser.getUserIcon()));
        }
        // 查询用户礼品卡
        List<FrontGiftInfo> frontGiftInfos = frontGiftInfoMapper.selectList(new LambdaQueryWrapper<FrontGiftInfo>().eq(FrontGiftInfo::getUserId, frontUser.getId()).in(FrontGiftInfo::getStatus, "0","1"));

        if (!frontGiftInfos.isEmpty()){
            frontUser.setGiftCard(frontGiftInfos.stream().map(FrontGiftInfo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        // 查询用户优惠券
        List<FrontCouponInfo> frontCouponInfos = frontCouponInfoMapper.selectList(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getUserId, frontUser.getId()).eq(FrontCouponInfo::getStatus, "0"));

        if (!frontCouponInfos.isEmpty()){
            frontUser.setCoupon(frontCouponInfos.size());
        }
        String token = SecurityUtils.getToken();
        frontUser.setToken(token);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        tokenService.refreshUniToken(loginUser);

        // 获取ip
        IPInfo ipInfo = IPInfoUtils.getIpInfo(IpUtils.getIpAddr());
        frontUser.setUserCity(ipInfo.getAddress());
        frontUser.setUserIp(IpUtils.getIpAddr());
        frontUserMapper.updateById(frontUser);
        return frontUser;
    }

    @Override
    public int updateUser(FrontUser frontUser) {
        Long userId = getUserId();
        if (frontUser.getUserIcon() != null){
            frontUser.setUserIcon(ossUrlCleanerUtil.cleanUrlsToString(frontUser.getUserIcon()));
        }
        frontUser.setId(userId);
        return frontUserMapper.updateFrontUser(frontUser);
    }

    @Override
    public UniUserVo.UserIncomeExpenditure listIncomeExpenditure(String date) {
        UniUserVo.UserIncomeExpenditure userIncomeExpenditure = new UniUserVo.UserIncomeExpenditure();
        LambdaQueryWrapper<FrontBalanceInfo> queryWrapper = getWrapper(date);
        List<FrontBalanceInfo> frontBalanceInfos = frontBalanceInfoMapper.selectList(queryWrapper);

        // 收入0和2 相加
        BigDecimal income = frontBalanceInfos.stream().filter(item -> item.getType() == 0 || item.getType() == 2).map(FrontBalanceInfo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 支出1 相加
        BigDecimal expenditure = frontBalanceInfos.stream().filter(item -> item.getType() == 1).map(FrontBalanceInfo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        userIncomeExpenditure.setIncome(income);
        userIncomeExpenditure.setExpenditure(expenditure);
        return userIncomeExpenditure;
    }

    @Override
    public List<UniUserVo.UserMoneyDetail> listMoneyDetail(String date) {
        List<UniUserVo.UserMoneyDetail> userMoneyDetails = new ArrayList<>();
        LambdaQueryWrapper<FrontBalanceInfo> queryWrapper = getWrapper(date);
        // 查询对应用户余额列表
        List<FrontBalanceInfo> frontBalanceInfos = frontBalanceInfoMapper.selectList(queryWrapper);

        // 遍历frontBalanceInfos
        frontBalanceInfos.forEach(item ->{
            // 通过订单号查询商品信息
            FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, item.getOrderNumber()));
            if (frontOrders != null){
                // 通过订单id查询商品信息
                LambdaQueryWrapper<FrontOrdersGoods> wrapper = new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId());
                if (item.getType() == 1){
                    List<FrontOrdersGoods> frontOrdersGoods = frontOrdersGoodsMapper.selectList(wrapper.eq(FrontOrdersGoods::getAfterStatus, 0));
                    UniUserVo.UserMoneyDetail goodsDetail = new UniUserVo.UserMoneyDetail();
                    goodsDetail.setTitle(item.getTitle());
                    goodsDetail.setDesc(getGoodsDetail(frontOrdersGoods));
                    goodsDetail.setMoney(new BigDecimal(item.getMoveaccount()));
                    goodsDetail.setTime(item.getCreateTime());
                    goodsDetail.setType(String.valueOf(item.getType()));

                    userMoneyDetails.add(goodsDetail);
                }
                if (item.getType() == 2){
                    List<FrontOrdersGoods> frontOrdersGoods = frontOrdersGoodsMapper.selectList(wrapper.eq(FrontOrdersGoods::getAfterStatus, 2));

                    UniUserVo.UserMoneyDetail goodsDetail = new UniUserVo.UserMoneyDetail();
                    goodsDetail.setTitle(item.getTitle());
                    goodsDetail.setDesc(getGoodsDetail(frontOrdersGoods));
                    goodsDetail.setMoney(new BigDecimal(item.getMoveaccount()));
                    goodsDetail.setTime(item.getCreateTime());
                    goodsDetail.setType(String.valueOf(item.getType()));
                    userMoneyDetails.add(goodsDetail);
                }
            }
            if (item.getType() == 0){
                UniUserVo.UserMoneyDetail userMoneyDetail = new UniUserVo.UserMoneyDetail();
                userMoneyDetail.setTitle(item.getTitle());
                userMoneyDetail.setMoney(new BigDecimal(item.getMoveaccount()));
                userMoneyDetail.setDesc("微信支付");
                userMoneyDetail.setTime(item.getCreateTime());
                userMoneyDetail.setType(String.valueOf(item.getType()));
                userMoneyDetails.add(userMoneyDetail);
            }

        });
        return userMoneyDetails;
    }

    @Override
    public List<UniUserVo.UserIntegralDetail> listIntegralDetail() {
        List<UniUserVo.UserIntegralDetail> userIntegralDetails = new ArrayList<>();

        // 查询用户积分明细
        List<FrontSource> frontSources = frontSourceMapper.selectList(new LambdaQueryWrapper<FrontSource>().eq(FrontSource::getUserId, getUserId()));

        frontSources.forEach(item ->{
            UniUserVo.UserIntegralDetail userIntegralDetail = new UniUserVo.UserIntegralDetail();
            userIntegralDetail.setType(String.valueOf(item.getType()));
            userIntegralDetail.setTime(item.getCreateTime());
            userIntegralDetail.setIntegral(BigDecimal.valueOf(item.getPoint()));
            userIntegralDetail.setSource(String.valueOf(item.getSource()));
            userIntegralDetails.add(userIntegralDetail);
        });

        return userIntegralDetails;
    }

    @Override
    public List<UniUserVo.UserGiftCardList> listGiftCardList(Integer type) {
        return frontGiftInfoMapper.listGiftCardList(getUserId(), type);
    }

    @Override
    public List<UniUserVo.UserGiftCardUseRecord> listGiftCardUseRecord(Integer id,String date) {
        // list
        List<UniUserVo.UserGiftCardUseRecord> userGiftCardUseRecords = new ArrayList<>();

        LambdaQueryWrapper<FrontGiftUserInfo> qw = new LambdaQueryWrapper<FrontGiftUserInfo>().eq(FrontGiftUserInfo::getGiftInfoId, id);

        if (StringUtils.isNotEmpty(date)) {
            try {
                YearMonth yearMonth = YearMonth.parse(date, DateTimeFormatter.ofPattern("yyyy-MM"));
                LocalDateTime start = yearMonth.atDay(1).atStartOfDay();
                LocalDateTime end = yearMonth.plusMonths(1).atDay(1).atStartOfDay();

                qw.ge(FrontGiftUserInfo::getCreateTime, start).lt(FrontGiftUserInfo::getCreateTime, end);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("日期格式不正确，应为 yyyy-MM 格式");
            }
        }

        // 查询对应用户礼品卡消费记录
        List<FrontGiftUserInfo> frontGiftUserInfos = frontGiftUserInfoMapper.selectList(qw);

        // 遍历
        frontGiftUserInfos.forEach(item ->{
            UniUserVo.UserGiftCardUseRecord userGiftCardUseRecord = new UniUserVo.UserGiftCardUseRecord();
            userGiftCardUseRecord.setMoney(item.getBalance());
            userGiftCardUseRecord.setTime(item.getCreateTime());
            userGiftCardUseRecord.setType(item.getType());
            FrontOrders frontOrders = frontOrdersMapper.selectOne(new LambdaQueryWrapper<FrontOrders>().eq(FrontOrders::getOrderNumber, item.getOrderNo()));
            if (frontOrders != null){
                LambdaQueryWrapper<FrontOrdersGoods> wrapper = new LambdaQueryWrapper<FrontOrdersGoods>().eq(FrontOrdersGoods::getOrderId, frontOrders.getId());
                if (item.getType() == 1){
                    List<FrontOrdersGoods> frontOrdersGoods = frontOrdersGoodsMapper.selectList(wrapper.eq(FrontOrdersGoods::getAfterStatus, 0));
                    userGiftCardUseRecord.setProduct(getGoodsDetail(frontOrdersGoods));
                }
                if (item.getType() == 2){
                    List<FrontOrdersGoods> frontOrdersGoods = frontOrdersGoodsMapper.selectList(wrapper.eq(FrontOrdersGoods::getAfterStatus, 2));
                    userGiftCardUseRecord.setProduct(getGoodsDetail(frontOrdersGoods));
                }
            }

            userGiftCardUseRecords.add(userGiftCardUseRecord);
        });
        return userGiftCardUseRecords;
    }

    @Override
    public List<UniUserVo.UserGiftCardBuyList> listGiftCardBuyList() {
        List<UniUserVo.UserGiftCardBuyList> userGiftCardBuyLists = new ArrayList<>();
        List<FrontGift> frontGifts = frontGiftMapper.selectList(new LambdaQueryWrapper<FrontGift>().eq(FrontGift::getIsDel, 0).eq(FrontGift::getIsShelf, 1));
        if (!frontGifts.isEmpty()){
            frontGifts.forEach(item ->{
                if (item.getTotal() > 0){
                    // 获取礼品卡已售数量
                    Long count = frontGiftInfoMapper.selectCount(new LambdaQueryWrapper<FrontGiftInfo>().eq(FrontGiftInfo::getGiftId, item.getId()));
                    // 剩余数量
                    if (count >= item.getTotal()) {
                        return;
                    }
                }

                UniUserVo.UserGiftCardBuyList buyList = new UniUserVo.UserGiftCardBuyList();
                buyList.setId(item.getId());
                buyList.setName(item.getName());
                buyList.setFaceValue(item.getBalance());
                buyList.setExpirationDate(item.getExpirationDate());
                userGiftCardBuyLists.add(buyList);
            });
        }
        return userGiftCardBuyLists;
    }

    @Override
    public List<UniUserVo.UserCouponList> listCouponList(Integer type) {
        List<UniUserVo.UserCouponList> userCouponLists = new ArrayList<>();
        if (type == 3){
            // 查询优惠券列表
            List<StoreFrontCoupon> frontCoupons = frontCouponMapper.selectList(null);
            frontCoupons.forEach(item ->{
                // 查询当前用户对应当前优惠券数量 和优惠券对应数量
                Long userCount = frontCouponInfoMapper.selectCount(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getCouponId, item.getId()).eq(FrontCouponInfo::getUserId, getUserId()));
                Long count = frontCouponInfoMapper.selectCount(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getCouponId, item.getId()));

                if (userCount >= item.getLimits() || count >= item.getTotal()){
                    return;
                }

                // 0-期限无限制
                if ("0".equals(item.getExpirationDate())){
                    UniUserVo.UserCouponList userCoupon = getUserCoupon(item);

                    if (userCoupon != null){
                        userCoupon.setExpireTime("长期有效");
                        userCouponLists.add(userCoupon);
                    }
                }else {
                    String[] split = item.getExpirationDate().split(",");
                    if (split.length == 1){
                        // 计算出剩余过期时间
                        LocalDateTime plusDays = item.getCreateTime().plusDays(Integer.parseInt(item.getExpirationDate()));

                        // 判断是否在当前时间之前
                        if (plusDays.isAfter(LocalDateTime.now())){
                            UniUserVo.UserCouponList userCoupon = getUserCoupon(item);
                            if (userCoupon != null) {
                                userCoupon.setExpireTime(item.getCreateTime() + "至" + plusDays);
                                userCouponLists.add(userCoupon);
                            }
                        }
                    }
                    if (split.length == 2){
                        // 判断当前时间是否在指定时间范围内
                        if (LocalDate.now().isAfter(LocalDate.parse(split[0])) && LocalDate.now().isBefore(LocalDate.parse(split[1]))){
                            UniUserVo.UserCouponList userCoupon = getUserCoupon(item);
                            if (userCoupon != null){
                                userCoupon.setExpireTime(split[0] + "至" + split[1]);
                                userCouponLists.add(userCoupon);
                            }
                        }
                    }
                }
            });
        }else {
            List<FrontCouponInfo> frontCouponInfos = frontCouponInfoMapper.selectList(
                    new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getUserId, getUserId())
                            .eq(FrontCouponInfo::getStatus, type));
            if (frontCouponInfos != null){
                frontCouponInfos.forEach(item ->{
                    UniUserVo.UserCouponList userCouponList = new UniUserVo.UserCouponList();
                    StoreFrontCoupon frontCoupon = frontCouponMapper.selectById(item.getCouponId());
                    userCouponList.setId(item.getCouponId());
                    userCouponList.setName(frontCoupon.getTitle());
                    userCouponList.setUsedType(String.valueOf(frontCoupon.getUsedType()));
                    userCouponList.setThreshold(frontCoupon.getThreshold());
                    userCouponList.setBalance(frontCoupon.getBalance());
                    userCouponList.setExpireTime(String.valueOf(item.getUseTime()));
                    userCouponLists.add(userCouponList);
                });
            }
        }

        return userCouponLists;
    }

    @Override
    public int receiveCoupon(UniUserVo.UserCouponList vo) {
        StoreFrontCoupon frontCoupon = frontCouponMapper.selectById(vo.getId());
        if (frontCoupon != null){
            // 查询当前用户对应当前优惠券数量 和优惠券对应数量
            Long userCount = frontCouponInfoMapper.selectCount(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getCouponId, vo.getId()).eq(FrontCouponInfo::getUserId, getUserId()));
            Long count = frontCouponInfoMapper.selectCount(new LambdaQueryWrapper<FrontCouponInfo>().eq(FrontCouponInfo::getCouponId, vo.getId()));

            if (userCount >= frontCoupon.getLimits() || count >= frontCoupon.getTotal()){
                throw new GlobalException("优惠券领取数量已到达规定数量");
            }
            FrontCouponInfo frontCouponInfo = new FrontCouponInfo();
            frontCouponInfo.setCouponId(vo.getId());
            frontCouponInfo.setType("0");
            frontCouponInfo.setUserId(getUserId());
            frontCouponInfo.setCreateTime(LocalDateTime.now());
            frontCouponInfo.setStatus("0");
            frontCouponInfo.setUseTime("长期有效".equals(vo.getExpireTime()) ? null : LocalDate.parse(vo.getExpireTime().split("至")[1]).atStartOfDay());
            frontCouponInfo.setBalance(frontCoupon.getBalance());
            frontCouponInfo.setOrderNumber(CommonUtil.getOrderNo("coupon"));
            return frontCouponInfoMapper.insert(frontCouponInfo);
        }
        return 0;
    }

    /**
     * 获取用户可领取优惠券
     * @param frontCoupon
     * @return
     */
    private UniUserVo.UserCouponList getUserCoupon(StoreFrontCoupon frontCoupon){

        // 判断类型是否为指定用户
        if (frontCoupon.getUsedType() == 3){
            // 将逗号分隔的字符串拆分为列表
            List<String> usedIds = Arrays.asList(frontCoupon.getUsed().split(","));

            // 检查是否包含用户ID
            if (!usedIds.contains(String.valueOf(getUserId()))) {
                return null;
            }
        }
        UniUserVo.UserCouponList userCouponList = new UniUserVo.UserCouponList();
        userCouponList.setId(frontCoupon.getId());
        userCouponList.setName(frontCoupon.getTitle());
        userCouponList.setUsedType(String.valueOf(frontCoupon.getUsedType()));
        userCouponList.setThreshold(frontCoupon.getThreshold());
        userCouponList.setBalance(frontCoupon.getBalance());
        return userCouponList;
    }

    /**
     * 获取商品明细
     */
    private String getGoodsDetail(List<FrontOrdersGoods> frontOrdersGoods) {
        List<Long> collect = new ArrayList<>();
        frontOrdersGoods.forEach(item -> {
            collect.add(item.getGoodsId());
        });

        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }

        List<FrontGoods> frontGoods = uniFrontGoodsMapper.selectList(new LambdaQueryWrapper<FrontGoods>().in(FrontGoods::getId, collect));

        return frontGoods.stream().map(FrontGoods::getName).collect(Collectors.joining(","));

    }

    private LambdaQueryWrapper<FrontBalanceInfo> getWrapper(String date) {
        LambdaQueryWrapper<FrontBalanceInfo> queryWrapper = new LambdaQueryWrapper<FrontBalanceInfo>().eq(FrontBalanceInfo::getUserId, getUserId());
        if (StringUtils.isNotBlank(date)) {
            try {
                YearMonth yearMonth = YearMonth.parse(date, DateTimeFormatter.ofPattern("yyyy-MM"));
                LocalDateTime start = yearMonth.atDay(1).atStartOfDay();
                LocalDateTime end = yearMonth.plusMonths(1).atDay(1).atStartOfDay();

                queryWrapper
                        .ge(FrontBalanceInfo::getCreateTime, start)
                        .lt(FrontBalanceInfo::getCreateTime, end);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("日期格式不正确，应为 yyyy-MM 格式");
            }
        }

        return queryWrapper;
    }

}
